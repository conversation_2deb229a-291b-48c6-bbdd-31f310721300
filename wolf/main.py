from __future__ import annotations

import argparse
import os

from src.clients.factory import Client
from src.utils.date_range import resolve_date_range
from src.utils.io import output_path


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description=(
            "Run endpoint crawler and write Excel under assets/data/<endpoint>.\n"
            "Defaults to last month if no date args are provided."
        )
    )
    parser.add_argument(
        "--endpoint",
        default=os.environ.get("ENDPOINT", "herold"),
        help="Endpoint/provider to crawl (default: herold)",
    )

    # Date arguments (one of the supported combinations)
    parser.add_argument("--start-date", dest="start_date", help="YYYY-MM-DD")
    parser.add_argument("--end-date", dest="end_date", help="YYYY-MM-DD")
    parser.add_argument("--last-days", dest="last_days", type=int)
    parser.add_argument("--last-month", dest="last_month", action="store_true")

    return parser.parse_args()


def main() -> None:
    args = parse_args()

    # Default behavior: if no date args passed, use last month
    if not any([args.start_date, args.end_date, args.last_days, args.last_month]):
        args.last_month = True

    # Resolve a concrete date range to pass to the client and for output naming
    dr = resolve_date_range(
        start_date=args.start_date,
        end_date=args.end_date,
        last_days=args.last_days,
        last_month=bool(args.last_month),
    )

    # For now only 'herold' is supported
    endpoint = args.endpoint.lower()
    if endpoint != "herold":
        raise SystemExit(
            f"Unsupported endpoint: {endpoint}. Only 'herold' is available."
        )

    client = Client(endpoint)

    # Pass explicit start/end (the client will use start_date for server-side filter)
    result = client.fetch(start_date=dr.start, end_date=dr.end)

    # Informative output
    out_path = output_path(dr.start, dr.end)
    print(f"Endpoint: {endpoint}")
    print(f"Date range: {dr.start} to {dr.end}")
    print(f"Number of fetched data: {len(result.records)}")

    print(
        f"Records: {len(result.records)}; IDs: {len(result.ids)}; Failed: {len(result.failed_ids)}"
    )
    print(f"Wrote: {out_path}")


if __name__ == "__main__":
    main()
