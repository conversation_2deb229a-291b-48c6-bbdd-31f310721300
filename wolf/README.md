# 🐺 Wolf Data Collection

<div align="center">

**A powerful, extensible data collection framework for Austrian business data**

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Status](https://img.shields.io/badge/Status-Active-brightgreen.svg)]()

</div>

---

## ✨ Features

🚀 **Simple CLI Interface** - Get started with a single command
🏭 **Factory-based Client API** - Clean, reusable architecture
📊 **Excel Output** - Professional reports ready for analysis
🔧 **Extensible Design** - Easy to add new data sources
📅 **Flexible Date Ranges** - Multiple ways to specify time periods

### 🎯 Currently Supported

- **Herold** (`herold`) - Scrapes evi.gv.at Neueintragungen (Austrian business registrations)

---

## 🚀 Quick Start

### Default Usage (Recommended)
Fetches last full calendar month from Herold endpoint:

```bash
uv run main.py
```

### 📅 Date Range Options

Choose the approach that fits your needs:

#### 📆 Explicit Date Range
```bash
uv run main.py --start-date 2025-10-01 --end-date 2025-10-31
```

#### ⏰ Last N Days
```bash
uv run main.py --last-days 14
```

#### 📅 Previous Month
```bash
uv run main.py --last-month
```

### 🎯 Specify Endpoint
```bash
uv run main.py --endpoint herold
```

### 📁 Output Location
Files are saved to: `assets/data/herold/Herold_Data_<DDMMYY>_<DDMMYY>.xlsx`

---

## 🛠️ CLI Reference

### Basic Syntax
```bash
uv run main.py [OPTIONS]
```

### 🏷️ Available Options

| Option | Description | Example |
|--------|-------------|---------|
| `--endpoint <name>` | Data source to use | `--endpoint herold` |
| `--start-date <date>` | Start date (YYYY-MM-DD) | `--start-date 2025-10-01` |
| `--end-date <date>` | End date (YYYY-MM-DD) | `--end-date 2025-10-31` |
| `--last-days <N>` | Last N days including today | `--last-days 7` |
| `--last-month` | Previous full calendar month | `--last-month` |

### 📊 Sample Output
```
✅ Collection Complete!
  Endpoint: herold
  Date range: 2025-09-01 to 2025-09-30
  Number of fetched data: 2793
  Records: 2793; IDs: 2794; Failed: 1
  📁 Wrote: assets/data/herold/Herold_Data_010925_300925.xlsx
```

---

## 🔧 Programmatic Usage (Client API)

Perfect for integration into your own applications!

### 📝 Basic Example

```python
from datetime import date
from src.clients.factory import Client
from src.utils.date_range import resolve_date_range

# 🏭 Create a client
client = Client("herold")

# 📅 Choose your date range (pick one method)

# Method 1: Explicit range
rng = resolve_date_range(
    start_date=date(2025, 10, 1),
    end_date=date(2025, 10, 31)
)

# Method 2: Last N days
# rng = resolve_date_range(last_days=14)

# Method 3: Previous month
# rng = resolve_date_range(last_month=True)

# 🚀 Fetch the data
result = client.fetch(start_date=rng.start, end_date=rng.end)

# 📊 Access your results
print(f"📈 Records: {len(result.records)}")
print(f"🆔 IDs collected: {len(result.ids)}")
print(f"❌ Failed: {len(result.failed_ids)}")
```

### 📦 Result Object Structure

| Property | Type | Description |
|----------|------|-------------|
| `result.records` | `list[dict]` | 📋 Parsed business detail records |
| `result.ids` | `list[str]` | 🆔 Internal IDs collected |
| `result.failed_ids` | `list[str]` | ❌ IDs that failed during fetch |

### 💡 Pro Tips

- 📊 **Excel Export**: Use `src/utils/io.py` for custom Excel output
- 🔧 **Custom Processing**: Process `result.records` with your own logic
- 🚀 **Future Features**: Clients may support additional flags in upcoming versions

---

## 📊 Output Format

### 📁 Herold Excel Structure

Each Excel file contains **two comprehensive sheets**:

| Sheet Name | Content | Description |
|------------|---------|-------------|
| `Unternehmensdaten` | 🏢 Main Dataset | Complete business information |
| `IDs` | 🆔 ID Tracking | Internal IDs with status and URLs |

### 📝 File Naming Convention
```
📁 Location: assets/data/herold/
📄 Format: Herold_Data_<DDMMYY>_<DDMMYY>.xlsx
📅 Example: Herold_Data_010925_300925.xlsx
```

---
## 🔌 Adding New Endpoints

Want to add support for a new data source? Here's your roadmap!

### 🏗️ Step-by-Step Guide

#### 1️⃣ Create Client Structure
```
src/clients/<your_endpoint>/
├── client.py      # 🏭 Main client (extends BaseClient)
├── sources.py     # 🔗 URL builders
└── parsers.py     # 🔍 BeautifulSoup parsers
```

#### 2️⃣ Register Your Client
Add to `src/clients/factory.py`

#### 3️⃣ Add Tests & Fixtures
```
tests/fixtures/<your_endpoint>/
└── sample_pages.html  # 📄 Test HTML snapshots
```

#### 4️⃣ Define Output Format
Keep Excel layout consistent or create new schema

### 🎯 Architecture Guidelines

| Component | Responsibility | Location |
|-----------|---------------|----------|
| 🌐 **Network** | Sessions, retries, requests | `client.py` |
| 🔗 **URLs** | Endpoint URL generation | `sources.py` |
| 🔍 **Parsing** | HTML/data extraction | `parsers.py` |
| 🛠️ **Utilities** | Shared helpers | `src/utils/` |

---


## 🔧 Troubleshooting

### 🚨 Common Issues & Solutions

#### 📊 Zero Records Returned

**Possible Causes:**
- 📅 Date range has no data
- 🚫 Website blocking/consent page
- 🔄 Layout changes

**Solutions:**
1. Try a known-good date range:
   ```bash
   uv run main.py --start-date 2025-09-01 --end-date 2025-09-30
   ```

2. Check debug output:
   - 🔍 Look for `assets/data/herold/_debug_listing.html`
   - 👀 Verify it's not a consent/interstitial page

3. Try different date ranges:
   ```bash
   uv run main.py --last-days 7
   uv run main.py --last-month
   ```

#### ❌ Failed ID Processing

**What happens:**
- Failed IDs are marked in the `IDs` sheet
- ✅ Re-running is safe (idempotent output naming)

**Check:**
- 📊 Review the `IDs` sheet for failure details
- 🔄 Re-run the same date range if needed

#### 🐌 Performance Issues

**Current behavior:**
- 📊 Progress bar shows collection status
- 🔧 Future: Add quiet mode flag for less logging

---

## 🎯 Herold Endpoint Details

### 🏢 Target Data Source
**evi.gv.at Neueintragungen** - Austrian business registrations

### 🛠️ Technical Implementation

| Aspect | Details |
|--------|---------|
| 🌐 **Strategy** | `requests` + `BeautifulSoup` (no browser needed) |
| 🔗 **Headers** | Lightweight `Referer` for navigation context |
| 🔍 **ID Collection** | Regex parsing from listing pages |
| 📄 **Detail Parsing** | Company/person section extraction |

### 🔧 Diagnostics & Debugging

- 🚨 **Zero IDs detected**: Auto-saves HTML to `_debug_listing.html`
- 📊 **Output tracking**: Two-sheet Excel with comprehensive data
- 📅 **Smart defaults**: Previous calendar month when no dates specified

### 📁 File Organization
```
assets/data/herold/
├── Herold_Data_010925_300925.xlsx  # 📊 Main output
└── _debug_listing.html             # 🔍 Debug file (if needed)
```

- Zero records:
- Re-run with a known-populated range: `--start-date 2025-09-01 --end-date 2025-09-30`.
- If page 1 has zero IDs, the first listing HTML is saved to `assets/data/herold/_debug_listing.html`. Open it to verify if it’s a consent/interstitial page or layout change.
- Try adding a different date range (`--last-days`, `--last-month`).
- One or more failed IDs:
- The `IDs` sheet marks failed IDs. You can re-run for the same range later; the client is idempotent for output naming.
- Performance: The collector shows a progress bar (tqdm). If you need less noise, we can add a flag to reduce logging.
---


## Herold (evi.gv.at) specifics

- Target: Neueintragungen on evi.gv.at

- Request strategy: requests + BeautifulSoup only (no browser). A lightweight `Referer` header is sent with listing requests to mimic basic navigation context.

- Parsing:
- Collect internal IDs from listing pages via regex on page text
- Fetch detail pages for each ID, parse company/person sections

- Diagnostics: If page 1 returns zero IDs, the client saves the HTML to `assets/data/herold/_debug_listing.html` and prints a one-line debug hint.

- Output: Excel with two sheets (`Unternehmensdaten`, `IDs`) under `assets/data/herold/`

- Naming: `Herold_Data_<DDMMYY>_<DDMMYY>.xlsx`

- Defaults: CLI defaults to previous full calendar month when no date flags are provided.

---

<div align="center">

**🐺 Wolf Data Collection - Built for Austrian Business Intelligence**

*Happy data hunting! 🚀*

</div>
