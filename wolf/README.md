# Wolf Data Collection

A small, extensible data collection framework with:
- A simple **CLI** you can run with `uv run main.py`
- A clean, reusable **Client API** (factory-based)
- **Excel output** written to `assets/data/<endpoint>/...`

Currently supported endpoint(s):
- `herold` (scrapes evi.gv.at Neueintragungen)

By default (no flags), the CLI fetches the last full calendar month and writes a two-sheet Excel file.


## TL;DR How to run

- Default (last full month, herold):

```bash
uv run main.py
```

- Explicit endpoint:

```bash
uv run main.py --endpoint herold
```

- Date overrides (choose one approach):

- Explicit range:

```bash
uv run main.py --start-date 2025-10-01 --end-date 2025-10-31
```

- Last N days (inclusive today):

```bash
uv run main.py --last-days 14
```

- Previous month (full calendar month):

```bash
uv run main.py --last-month
```

Output path (example for herold):

- `assets/data/herold/Herold_Data_<DDMMYY>_<DDMMYY>.xlsx`

---


## CLI usage

```bash
uv run main.py [--endpoint <name>] [--start-date YYYY-MM-DD --end-date YYYY-MM-DD | --last-days N | --last-month]
```


Flags:
- `--endpoint <name>`: Which provider to run. Default: `herold`.

- Date selection:

- `--start-date` + `--end-date`: Explicit inclusive date range
- `--last-days N`: From today-N to today (inclusive)
- `--last-month`: Full previous calendar month


Behavior:

- If no date options are supplied, the CLI defaults to `--last-month`.
- Prints a short summary including counts and the Excel output path, e.g.
```
  Endpoint: herold
  Date range: 2025-09-01 to 2025-09-30
  Number of fetched data: 2793
  Records: 2793; IDs: 2794; Failed: 1
  Wrote: assets/data/herold/Herold_Data_010925_300925.xlsx
```

---



## Programmatic usage (Client API)



The codebase exposes a small factory and a consistent client interface.



```python

from datetime import date

from src.clients.factory import Client
from src.utils.date_range import resolve_date_range


# Create a client (currently only "herold")
client = Client("herold")

# Choose a date range (3 ways)

# 1) explicit range
rng = resolve_date_range(start_date=date(2025, 10, 1), end_date=date(2025, 10, 31))

# 2) last N days
# rng = resolve_date_range(last_days=14)

# 3) previous month
# rng = resolve_date_range(last_month=True)

# Fetch data
result = client.fetch(start_date=rng.start, end_date=rng.end)

# Result object
# result.records -> list[dict] of parsed detail records
# result.ids -> list[str] of internal IDs collected
# result.failed_ids -> list[str] of IDs that failed during detail fetch
```

Notes:

- Excel writing is handled by the client run invoked from the CLI. If you consume the API directly, you can write your own output or call the project utility under `src/utils/io.py`.

- Clients typically accept `start_date` and `end_date`. Some may offer additional feature flags in the future.
---


## Output format

For `herold`, the Excel contains two sheets:

- `Unternehmensdaten` (main dataset)
- `IDs` (internal IDs with status and detail page URL)

Filename format: `Herold_Data_<DDMMYY>_<DDMMYY>.xlsx` under `assets/data/herold/`.


---
## Adding a new endpoint (client)

1) Create a new folder under `src/clients/<name>/` with at least:
- `client.py`: subclass of `BaseClient` implementing `fetch(...)`
- `sources.py`: URL builders for listing/detail endpoints
- `parsers.py`: BeautifulSoup parsers for listing and detail pages
2) Register the client in `src/clients/factory.py`.
3) Provide tests and simple fixtures (HTML snapshots) under `tests/fixtures/<name>/`.
4) Keep the Excel output layout consistent or define a new one if required.

Guidelines:

- Keep network concerns in the client (session, retries) and URL generation in `sources.py`.
- Keep parsing concerns isolated in `parsers.py`.
- Reuse shared utilities (`date_range`, `io`, `http/session`).
---


## Troubleshooting

- Zero records:
- Re-run with a known-populated range: `--start-date 2025-09-01 --end-date 2025-09-30`.
- If page 1 has zero IDs, the first listing HTML is saved to `assets/data/herold/_debug_listing.html`. Open it to verify if it’s a consent/interstitial page or layout change.
- Try adding a different date range (`--last-days`, `--last-month`).
- One or more failed IDs:
- The `IDs` sheet marks failed IDs. You can re-run for the same range later; the client is idempotent for output naming.
- Performance: The collector shows a progress bar (tqdm). If you need less noise, we can add a flag to reduce logging.
---


## Herold (evi.gv.at) specifics

- Target: Neueintragungen on evi.gv.at

- Request strategy: requests + BeautifulSoup only (no browser). A lightweight `Referer` header is sent with listing requests to mimic basic navigation context.

- Parsing:
- Collect internal IDs from listing pages via regex on page text
- Fetch detail pages for each ID, parse company/person sections

- Diagnostics: If page 1 returns zero IDs, the client saves the HTML to `assets/data/herold/_debug_listing.html` and prints a one-line debug hint.

- Output: Excel with two sheets (`Unternehmensdaten`, `IDs`) under `assets/data/herold/`

- Naming: `Herold_Data_<DDMMYY>_<DDMMYY>.xlsx`

- Defaults: CLI defaults to previous full calendar month when no date flags are provided.
