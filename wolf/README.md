# Wolf Data Collection

Personal data collection framework for Austrian business data.

## Quick Usage

**Default (last month, herold):**
```bash
uv run main.py
```

**Date options:**
```bash
# Specific range
uv run main.py --start-date 2025-10-01 --end-date 2025-10-31

# Last 14 days
uv run main.py --last-days 14

# Previous month
uv run main.py --last-month
```

**Output:** `assets/data/herold/Herold_Data_<DDMMYY>_<DDMMYY>.xlsx`

## Programmatic Usage

```python
from datetime import date
from src.clients.factory import Client
from src.utils.date_range import resolve_date_range

# Create client
client = Client("herold")

# Set date range
rng = resolve_date_range(start_date=date(2025, 10, 1), end_date=date(2025, 10, 31))
# or: rng = resolve_date_range(last_days=14)
# or: rng = resolve_date_range(last_month=True)

# Fetch data
result = client.fetch(start_date=rng.start, end_date=rng.end)

# Access results
print(f"Records: {len(result.records)}")
print(f"Failed: {len(result.failed_ids)}")
```

## Adding New Crawlers

When you want to add a new data source:

### 1. Create folder structure:
```
src/clients/<new_name>/
├── client.py      # Main client class
├── sources.py     # URL builders
└── parsers.py     # HTML parsers
```

### 2. Implement the files:

**client.py:**
```python
from src.clients.base import BaseClient

class YourClient(BaseClient):
    def fetch(self, start_date, end_date):
        # Your implementation
        pass
```

**sources.py:**
```python
def build_listing_url(date, page=1):
    # Return URL for listing page
    pass

def build_detail_url(item_id):
    # Return URL for detail page
    pass
```

**parsers.py:**
```python
from bs4 import BeautifulSoup

def parse_listing_page(html):
    # Extract IDs from listing
    pass

def parse_detail_page(html):
    # Extract data from detail page
    pass
```

### 3. Register in factory:
Add to `src/clients/factory.py`:
```python
from src.clients.your_name.client import YourClient

def Client(name: str):
    if name == "your_name":
        return YourClient()
    # ... existing clients
```

### 4. Test with fixtures:
Create `tests/fixtures/<new_name>/sample.html` with test HTML

## Performance Features

**Concurrent Requests** - Fetches multiple detail pages simultaneously (5-10x faster)
**Smart Caching** - Resumes interrupted runs, skips already-fetched records
**Enhanced Error Handling** - Graceful handling of 404s, rate limits, timeouts
**Adaptive Rate Limiting** - Automatically adjusts request speed if rate limited

## Current Endpoints

**herold** - evi.gv.at Neueintragungen
- Scrapes Austrian business registrations
- Uses requests + BeautifulSoup with concurrent fetching
- Outputs 2-sheet Excel: main data + ID tracking
- Supports progress caching and resume

## Troubleshooting

**Zero records:**
- Try known date range: `--start-date 2025-09-01 --end-date 2025-09-30`
- Check `assets/data/herold/_debug_listing.html` for consent pages

**Failed IDs:**
- Check the "IDs" sheet in Excel output
- Re-run is safe (same filename)

**Performance:**
- Progress bar shows status
- Failed IDs are tracked and can be retried
- Use cache manager: `python cache_manager.py list` to see cached runs

## Cache Management

**List cached operations:**
```bash
python cache_manager.py list
```

**Clear specific cache:**
```bash
python cache_manager.py clear --client herold --start-date 2025-10-01 --end-date 2025-10-31
```

**Clear all cache:**
```bash
python cache_manager.py clear --all
```