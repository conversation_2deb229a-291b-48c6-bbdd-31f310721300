from __future__ import annotations

from urllib.parse import urlencode


BASE = "https://www.evi.gv.at"


def listing_url(*, page: int, datum_von: str) -> str:
    """Build listing URL for new company entries starting from a given date.

    Args:
        page: int
            1-based page index.
        datum_von: str
            Start date in YYYY-MM-DD.

    Returns:
        URL string.
    """
    query = {
        "page": page,
        "filter": "Kategorien:Unternehmen | Neueintragungen",
        "datumVon": datum_von,
    }
    return f"{BASE}/s?{urlencode(query)}"


def detail_url(oid: str) -> str:
    """Build detail URL for a given internal oid."""
    return f"{BASE}/f/{oid}"
