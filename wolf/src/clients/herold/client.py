from __future__ import annotations

from dataclasses import dataclass
from typing import Optional, List
import logging

import pandas as pd
import requests
from tqdm import tqdm

from . import sources
from .parsers import parse_listing_for_ids, parse_detail
from ..base import BaseClient, FetchResult
from ...http.session import build_session
from ...utils.date_range import resolve_date_range, DateRange
from ...utils import io as io_utils
from ...utils.cache import ProgressCache

# Additional imports for performance improvements

# Configure logging for better error tracking
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class HeroldClient(BaseClient):
    """Client for scraping evi.gv.at (Herold notebook refactor) using BeautifulSoup.

    The client replicates the Selenium-based notebook logic:
    - Build listing URLs starting from a given date (datumVon)
    - Extract internal IDs from listing pages
    - Fetch detail pages and parse organisational and personal data
    - Construct a DataFrame with the same columns and ordering
    - Write an Excel file to assets/data/herold with a start/end date suffix

    Notes
    -----
    - evi.gv.at "Neueintragungen" listing accepts only a start date (datumVon).
      We therefore use the resolved start_date for the server-side filter and
      treat end_date for naming and potential local filtering in the future.
    - To enable offline testing, an optional requests.Session can be injected.
    """

    name: str = "herold"
    session: Optional[requests.Session] = None
    cache: Optional[ProgressCache] = None

    def _get_session(self) -> requests.Session:
        """Get a configured requests.Session, either injected or built."""
        return self.session or build_session()

    def _get_cache(self) -> ProgressCache:
        """Get or create the progress cache."""
        if self.cache is None:
            self.cache = ProgressCache()
        return self.cache

    def _collect_ids(self, *, start_date_str: str) -> List[str]:
        """Collect internal IDs from listing pages."""
        session = self._get_session()
        ids: List[str] = []
        page = 1
        # Loop over pages until no IDs found
        while True:
            url = sources.listing_url(page=page, datum_von=start_date_str)
            resp = session.get(url, headers={"Referer": sources.BASE + "/"})
            resp.raise_for_status()
            page_ids = parse_listing_for_ids(resp.text)
            if not page_ids:
                # Write debug HTML for page 1 when no IDs found
                if page == 1:
                    dbg = io_utils.ensure_assets_dir() / "_debug_listing.html"
                    try:
                        dbg.write_text(resp.text, encoding="utf-8")
                        print(f"[debug] No IDs on page 1. Saved HTML to: {dbg}")
                    except Exception:
                        pass
                break
            ids.extend(page_ids)
            page += 1
        # De-duplicate preserving order
        seen = set()
        ordered = []
        for oid in ids:
            if oid not in seen:
                seen.add(oid)
                ordered.append(oid)
        return ordered

    def _fetch_single_record(
        self, oid: str, session: requests.Session
    ) -> tuple[Optional[dict], Optional[str]]:
        """Fetch a single record by ID with enhanced error handling.

        Returns:
            tuple: (record_dict, failed_id) - one will be None
        """
        try:
            url = sources.detail_url(oid)
            resp = session.get(url)

            # Handle different HTTP status codes appropriately
            if resp.status_code == 404:
                logger.debug(f"ID {oid} returned 404 - likely deleted/moved")
                return None, oid
            elif resp.status_code == 403:
                logger.warning(
                    f"ID {oid} returned 403 - access forbidden, possible rate limiting"
                )
                return None, oid
            elif resp.status_code == 429:
                logger.warning(f"ID {oid} returned 429 - rate limited")
                return None, oid
            elif resp.status_code >= 500:
                logger.warning(f"ID {oid} returned {resp.status_code} - server error")
                return None, oid

            resp.raise_for_status()

            # Validate response content
            if not resp.text or len(resp.text.strip()) < 100:
                logger.warning(
                    f"ID {oid} returned suspiciously short content ({len(resp.text)} chars)"
                )
                return None, oid

            record = parse_detail(resp.text, oid)

            # Validate parsed record
            if not record or not record.get("Unternehmen"):
                logger.warning(
                    f"ID {oid} parsed but missing company name - possible parsing issue"
                )
                return None, oid

            record["Interne ID"] = oid
            return record, None

        except requests.exceptions.Timeout as e:
            logger.warning(f"Timeout for ID {oid}: {e}")
            return None, oid
        except requests.exceptions.ConnectionError as e:
            logger.warning(f"Connection error for ID {oid}: {e}")
            return None, oid
        except requests.exceptions.RequestException as e:
            logger.warning(f"Network error for ID {oid}: {e}")
            return None, oid
        except Exception as e:
            logger.error(f"Unexpected error parsing ID {oid}: {e}")
            return None, oid

    def _collect_records(
        self,
        ids: List[str],
        max_workers: int = 10,
        delay_between_requests: float = 0.05,
    ) -> tuple[list[dict], list[str]]:
        """Collect records for the given IDs using concurrent requests with adaptive rate limiting.

        Args:
            ids: List of IDs to fetch
            max_workers: Maximum number of concurrent threads
            delay_between_requests: Delay between requests to be respectful to server

        Returns:
            records: list[dict]
                List of records with "Interne ID" key added.
            failed: list[str]
                List of IDs that failed to fetch.
        """
        from concurrent.futures import ThreadPoolExecutor, as_completed
        import time

        if not ids:
            logger.info("No IDs to fetch")
            return [], []

        session = self._get_session()
        failed: List[str] = []
        records: list[dict] = []
        rate_limit_errors = 0

        logger.info(
            f"Starting concurrent fetch of {len(ids)} records with {max_workers} workers"
        )

        # Use ThreadPoolExecutor for concurrent requests
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_id = {
                executor.submit(self._fetch_single_record, oid, session): oid
                for oid in ids
            }

            # Process completed tasks with progress bar
            with tqdm(total=len(ids), desc="Fetching Records", ncols=100) as pbar:
                for future in as_completed(future_to_id):
                    record, failed_id = future.result()

                    if record:
                        records.append(record)
                    if failed_id:
                        failed.append(failed_id)
                        # Check if this was a rate limiting issue
                        if "429" in str(failed_id) or "403" in str(failed_id):
                            rate_limit_errors += 1

                    pbar.update(1)

                    # Adaptive delay - increase if we're getting rate limited
                    current_delay = delay_between_requests
                    if rate_limit_errors > 5:
                        current_delay *= 2
                        logger.warning(
                            f"Increased delay to {current_delay}s due to rate limiting"
                        )

                    time.sleep(current_delay)

        success_rate = len(records) / len(ids) * 100 if ids else 0
        logger.info(
            f"Fetch complete: {len(records)} successful ({success_rate:.1f}%), "
            f"{len(failed)} failed, {rate_limit_errors} rate limit errors"
        )

        return records, failed

    def _build_dataframe(
        self, records: list[dict], ids: list[str]
    ) -> tuple[pd.DataFrame, pd.DataFrame]:
        """Build DataFrame with columns matching the original notebook logic.

        The IDs sheet is also built here to avoid looping over IDs again.
        The structure is identical to the notebook's output and predefined
        by requirements.
        """
        # Main DataFrame (robust to empty records)
        df = pd.DataFrame.from_records(records)
        if "Interne ID" in df.columns:
            df = df.set_index("Interne ID").reset_index()
        else:
            # Ensure column exists for downstream operations
            df["Interne ID"] = pd.Series(dtype=object)
        df["Art"] = "Neueintragungen"

        # Derive columns from notebook logic
        if "Adresse" in df.columns:
            df["Straße und Hausnummer"] = (
                df["Adresse"].str.split(",").str[0].str.strip()
            )
            df["Ort und PLZ"] = df["Adresse"].str.split(",").str[1].str.strip()
        cols_first = [
            "Unternehmen",
            "Straße und Hausnummer",
            "Ort und PLZ",
            "Adresse",
            "Sitz",
        ]
        cols_last = ["Status", "Interne ID"]

        # Keep order stable and rename Adresse -> Volle Adresse as in notebook
        existing_cols_first = [c for c in cols_first if c in df.columns]
        existing_cols_last = [c for c in cols_last if c in df.columns]
        last_cols = (
            df[existing_cols_last].copy()
            if existing_cols_last
            else pd.DataFrame(index=df.index)
        )
        ordered = list(dict.fromkeys(existing_cols_first + list(df.columns)))
        drop = [c for c in existing_cols_last if c in ordered]
        df = (
            df[ordered]
            .drop(columns=drop, errors="ignore")
            .rename(columns={"Adresse": "Volle Adresse"})
        )
        for c in existing_cols_last:
            df[c] = last_cols[c]

        # IDs sheet
        ser_ids = pd.DataFrame(
            {
                "ids": ids,
                "url": [sources.detail_url(oid) for oid in ids],
                "status": "success",
            }
        )
        failed_mask = ~ser_ids["ids"].isin(df["Interne ID"])
        ser_ids.loc[failed_mask, "status"] = "failed"
        return df, ser_ids

    def fetch(
        self,
        *,
        start_date: Optional[str | object] = None,
        end_date: Optional[str | object] = None,
        last_days: Optional[int] = None,
        last_month: bool = False,
    ) -> FetchResult:
        """Fetch Herold data and return structured results.
        Main method orchestrating the data collection and processing steps.

        Process contains the following:
            1. Resolve date range
            2. Collect internal IDs from listing pages
            3. Fetch detail pages and parse records
            4. Build DataFrame with columns matching the original notebook logic
            5. Write Excel file to assets/data/herold

        Args:
            start_date, end_date: date | str | None
                If provided, must be ISO-8601 (YYYY-MM-DD) strings or date objects.
            last_days: int | None
                Number of days back from today.
            last_month: bool
                If True, selects the full previous calendar month.

        Returns:
            FetchResult with records, IDs, and failed IDs.
        """
        # Resolve date range based on args
        dr: DateRange = resolve_date_range(
            start_date=start_date,
            end_date=end_date,
            last_days=last_days,
            last_month=last_month,
        )
        start_str = dr.start.strftime("%Y-%m-%d")
        end_str = dr.end.strftime("%Y-%m-%d")

        # Collect all IDs first
        ids = self._collect_ids(start_date_str=start_str)
        logger.info(
            f"Found {len(ids)} total IDs for date range {start_str} to {end_str}"
        )

        # Check cache for existing progress
        cache = self._get_cache()
        remaining_ids, cached_records, cached_failed = cache.get_remaining_ids(
            self.name, start_str, end_str, ids
        )

        # Fetch remaining records
        if remaining_ids:
            logger.info(f"Fetching {len(remaining_ids)} remaining records")
            new_records, new_failed = self._collect_records(remaining_ids)
        else:
            logger.info("All records already cached!")
            new_records, new_failed = [], []

        # Combine cached and new results
        all_records = cached_records + new_records
        all_failed = list(set(cached_failed + new_failed))  # Remove duplicates

        # Save progress to cache
        successful_ids = [r["Interne ID"] for r in all_records]
        cache.save_progress(
            self.name, start_str, end_str, successful_ids, all_failed, all_records, ids
        )

        df, ser_ids = self._build_dataframe(all_records, ids)
        # Write artifacts
        io_utils.write_excel(
            df=df, ser_ids=ser_ids, start_date=dr.start, end_date=dr.end
        )

        return FetchResult(records=all_records, ids=ids, failed_ids=all_failed)
