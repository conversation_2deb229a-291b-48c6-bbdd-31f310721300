from __future__ import annotations

from dataclasses import dataclass
from typing import Optional, List

import pandas as pd
import requests
from tqdm import tqdm

from . import sources
from .parsers import parse_listing_for_ids, parse_detail
from ..base import BaseClient, FetchResult
from ...http.session import build_session
from ...utils.date_range import resolve_date_range, DateRange
from ...utils import io as io_utils


@dataclass
class HeroldClient(BaseClient):
    """Client for scraping evi.gv.at (Herold notebook refactor) using BeautifulSoup.

    The client replicates the Selenium-based notebook logic:
    - Build listing URLs starting from a given date (datumVon)
    - Extract internal IDs from listing pages
    - Fetch detail pages and parse organisational and personal data
    - Construct a DataFrame with the same columns and ordering
    - Write an Excel file to assets/data/herold with a start/end date suffix

    Notes
    -----
    - evi.gv.at "Neueintragungen" listing accepts only a start date (datumVon).
      We therefore use the resolved start_date for the server-side filter and
      treat end_date for naming and potential local filtering in the future.
    - To enable offline testing, an optional requests.Session can be injected.
    """

    name: str = "herold"
    session: Optional[requests.Session] = None

    def _get_session(self) -> requests.Session:
        """Get a configured requests.Session, either injected or built."""
        return self.session or build_session()

    def _collect_ids(self, *, start_date_str: str) -> List[str]:
        """Collect internal IDs from listing pages."""
        session = self._get_session()
        ids: List[str] = []
        page = 1
        # Loop over pages until no IDs found
        while True:
            url = sources.listing_url(page=page, datum_von=start_date_str)
            resp = session.get(url, headers={"Referer": sources.BASE + "/"})
            resp.raise_for_status()
            page_ids = parse_listing_for_ids(resp.text)
            if not page_ids:
                # Write debug HTML for page 1 when no IDs found
                if page == 1:
                    dbg = io_utils.ensure_assets_dir() / "_debug_listing.html"
                    try:
                        dbg.write_text(resp.text, encoding="utf-8")
                        print(f"[debug] No IDs on page 1. Saved HTML to: {dbg}")
                    except Exception:
                        pass
                break
            ids.extend(page_ids)
            page += 1
        # De-duplicate preserving order
        seen = set()
        ordered = []
        for oid in ids:
            if oid not in seen:
                seen.add(oid)
                ordered.append(oid)
        return ordered

    def _collect_records(self, ids: List[str]) -> tuple[list[dict], list[str]]:
        """Collect records for the given IDs.

        Returns:
            records: list[dict]
                List of records with "Interne ID" key added.
            failed: list[str]
                List of IDs that failed to fetch.
        """
        session = self._get_session()
        failed: List[str] = []
        records: list[dict] = []

        # Loop over IDs with progress bar
        for oid in tqdm(ids, desc="Progress Data Collection", ncols=100):
            # TODO: Move to retry session
            # TODO: Handle 404s gracefully
            try:
                url = sources.detail_url(oid)
                resp = session.get(url)
                resp.raise_for_status()
                record = parse_detail(resp.text, oid)
                record["Interne ID"] = oid
                records.append(record)
            # TODO: Narrow exception type
            # TODO: Log exception
            except Exception:
                failed.append(oid)
        return records, failed

    def _build_dataframe(
        self, records: list[dict], ids: list[str]
    ) -> tuple[pd.DataFrame, pd.DataFrame]:
        """Build DataFrame with columns matching the original notebook logic.

        The IDs sheet is also built here to avoid looping over IDs again.
        The structure is identical to the notebook's output and predefined
        by requirements.
        """
        # Main DataFrame (robust to empty records)
        df = pd.DataFrame.from_records(records)
        if "Interne ID" in df.columns:
            df = df.set_index("Interne ID").reset_index()
        else:
            # Ensure column exists for downstream operations
            df["Interne ID"] = pd.Series(dtype=object)
        df["Art"] = "Neueintragungen"

        # Derive columns from notebook logic
        if "Adresse" in df.columns:
            df["Straße und Hausnummer"] = (
                df["Adresse"].str.split(",").str[0].str.strip()
            )
            df["Ort und PLZ"] = df["Adresse"].str.split(",").str[1].str.strip()
        cols_first = [
            "Unternehmen",
            "Straße und Hausnummer",
            "Ort und PLZ",
            "Adresse",
            "Sitz",
        ]
        cols_last = ["Status", "Interne ID"]

        # Keep order stable and rename Adresse -> Volle Adresse as in notebook
        existing_cols_first = [c for c in cols_first if c in df.columns]
        existing_cols_last = [c for c in cols_last if c in df.columns]
        last_cols = (
            df[existing_cols_last].copy()
            if existing_cols_last
            else pd.DataFrame(index=df.index)
        )
        ordered = list(dict.fromkeys(existing_cols_first + list(df.columns)))
        drop = [c for c in existing_cols_last if c in ordered]
        df = (
            df[ordered]
            .drop(columns=drop, errors="ignore")
            .rename(columns={"Adresse": "Volle Adresse"})
        )
        for c in existing_cols_last:
            df[c] = last_cols[c]

        # IDs sheet
        ser_ids = pd.DataFrame(
            {
                "ids": ids,
                "url": [sources.detail_url(oid) for oid in ids],
                "status": "success",
            }
        )
        failed_mask = ~ser_ids["ids"].isin(df["Interne ID"])
        ser_ids.loc[failed_mask, "status"] = "failed"
        return df, ser_ids

    def fetch(
        self,
        *,
        start_date: Optional[str | object] = None,
        end_date: Optional[str | object] = None,
        last_days: Optional[int] = None,
        last_month: bool = False,
    ) -> FetchResult:
        """Fetch Herold data and return structured results.
        Main method orchestrating the data collection and processing steps.

        Process contains the following:
            1. Resolve date range
            2. Collect internal IDs from listing pages
            3. Fetch detail pages and parse records
            4. Build DataFrame with columns matching the original notebook logic
            5. Write Excel file to assets/data/herold

        Args:
            start_date, end_date: date | str | None
                If provided, must be ISO-8601 (YYYY-MM-DD) strings or date objects.
            last_days: int | None
                Number of days back from today.
            last_month: bool
                If True, selects the full previous calendar month.

        Returns:
            FetchResult with records, IDs, and failed IDs.
        """
        # Resolve date range based on args
        dr: DateRange = resolve_date_range(
            start_date=start_date,
            end_date=end_date,
            last_days=last_days,
            last_month=last_month,
        )
        start_str = dr.start.strftime("%Y-%m-%d")

        ids = self._collect_ids(start_date_str=start_str)
        records, failed = self._collect_records(ids)

        df, ser_ids = self._build_dataframe(records, ids)
        # Write artifacts
        io_utils.write_excel(
            df=df, ser_ids=ser_ids, start_date=dr.start, end_date=dr.end
        )

        return FetchResult(records=records, ids=ids, failed_ids=failed)
