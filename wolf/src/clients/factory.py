from __future__ import annotations

from typing import Literal

from .base import BaseClient
from .herold.client import HeroldClient


ClientName = Literal["herold"]


def Client(name: ClientName) -> BaseClient:
    """Factory function returning the requested client.

    Args:
        name: ClientName
            Name of the client to return.

    Returns:
        BaseClient subclass instance.

    Example:
        client = Client("herold")
        data = client.fetch(last_month=True)
    """
    if name == "herold":
        return HeroldClient()
    raise ValueError(f"Unknown client: {name}")
