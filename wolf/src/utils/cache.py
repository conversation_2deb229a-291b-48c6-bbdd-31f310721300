"""Progress persistence and caching utilities for Wolf data collection."""

from __future__ import annotations

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Set
from datetime import datetime
import hashlib

logger = logging.getLogger(__name__)


class ProgressCache:
    """Simple file-based cache for tracking fetch progress and resuming interrupted runs."""
    
    def __init__(self, cache_dir: Path = None):
        """Initialize the progress cache.
        
        Args:
            cache_dir: Directory to store cache files. Defaults to assets/cache
        """
        if cache_dir is None:
            cache_dir = Path("assets/cache")
        
        self.cache_dir = cache_dir
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
    def _get_cache_key(self, client_name: str, start_date: str, end_date: str) -> str:
        """Generate a unique cache key for a fetch operation."""
        key_data = f"{client_name}_{start_date}_{end_date}"
        return hashlib.md5(key_data.encode()).hexdigest()[:12]
    
    def _get_cache_file(self, cache_key: str) -> Path:
        """Get the cache file path for a given cache key."""
        return self.cache_dir / f"progress_{cache_key}.json"
    
    def save_progress(
        self, 
        client_name: str, 
        start_date: str, 
        end_date: str,
        successful_ids: List[str],
        failed_ids: List[str],
        records: List[Dict],
        total_ids: List[str]
    ) -> None:
        """Save progress for a fetch operation.
        
        Args:
            client_name: Name of the client (e.g., 'herold')
            start_date: Start date string
            end_date: End date string
            successful_ids: List of successfully fetched IDs
            failed_ids: List of failed IDs
            records: List of successfully fetched records
            total_ids: Complete list of IDs that were supposed to be fetched
        """
        cache_key = self._get_cache_key(client_name, start_date, end_date)
        cache_file = self._get_cache_file(cache_key)
        
        progress_data = {
            "client_name": client_name,
            "start_date": start_date,
            "end_date": end_date,
            "timestamp": datetime.now().isoformat(),
            "total_ids": total_ids,
            "successful_ids": successful_ids,
            "failed_ids": failed_ids,
            "records": records,
            "completion_rate": len(successful_ids) / len(total_ids) if total_ids else 0
        }
        
        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(progress_data, f, indent=2, ensure_ascii=False)
            logger.info(f"Progress saved to {cache_file}")
        except Exception as e:
            logger.error(f"Failed to save progress: {e}")
    
    def load_progress(
        self, 
        client_name: str, 
        start_date: str, 
        end_date: str
    ) -> Optional[Dict]:
        """Load progress for a fetch operation.
        
        Args:
            client_name: Name of the client
            start_date: Start date string
            end_date: End date string
            
        Returns:
            Progress data dict or None if no cache exists
        """
        cache_key = self._get_cache_key(client_name, start_date, end_date)
        cache_file = self._get_cache_file(cache_key)
        
        if not cache_file.exists():
            return None
            
        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                progress_data = json.load(f)
            logger.info(f"Progress loaded from {cache_file}")
            return progress_data
        except Exception as e:
            logger.error(f"Failed to load progress: {e}")
            return None
    
    def get_remaining_ids(
        self, 
        client_name: str, 
        start_date: str, 
        end_date: str,
        all_ids: List[str]
    ) -> tuple[List[str], List[Dict], List[str]]:
        """Get IDs that still need to be fetched, plus any cached records.
        
        Args:
            client_name: Name of the client
            start_date: Start date string
            end_date: End date string
            all_ids: Complete list of IDs to fetch
            
        Returns:
            tuple: (remaining_ids, cached_records, cached_failed_ids)
        """
        progress = self.load_progress(client_name, start_date, end_date)
        
        if not progress:
            return all_ids, [], []
        
        successful_ids = set(progress.get("successful_ids", []))
        failed_ids = progress.get("failed_ids", [])
        cached_records = progress.get("records", [])
        
        # Only retry failed IDs if they're not too old (could be temporary failures)
        remaining_ids = [
            id for id in all_ids 
            if id not in successful_ids
        ]
        
        logger.info(
            f"Cache hit: {len(successful_ids)} already fetched, "
            f"{len(remaining_ids)} remaining, {len(failed_ids)} previously failed"
        )
        
        return remaining_ids, cached_records, failed_ids
    
    def clear_cache(self, client_name: str, start_date: str, end_date: str) -> None:
        """Clear cache for a specific fetch operation."""
        cache_key = self._get_cache_key(client_name, start_date, end_date)
        cache_file = self._get_cache_file(cache_key)
        
        if cache_file.exists():
            cache_file.unlink()
            logger.info(f"Cache cleared: {cache_file}")
    
    def list_cached_operations(self) -> List[Dict]:
        """List all cached operations with their metadata."""
        cached_ops = []
        
        for cache_file in self.cache_dir.glob("progress_*.json"):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                cached_ops.append({
                    "file": cache_file.name,
                    "client": data.get("client_name"),
                    "start_date": data.get("start_date"),
                    "end_date": data.get("end_date"),
                    "timestamp": data.get("timestamp"),
                    "completion_rate": data.get("completion_rate", 0),
                    "total_records": len(data.get("records", [])),
                    "failed_count": len(data.get("failed_ids", []))
                })
            except Exception as e:
                logger.warning(f"Could not read cache file {cache_file}: {e}")
        
        return sorted(cached_ops, key=lambda x: x.get("timestamp", ""), reverse=True)
