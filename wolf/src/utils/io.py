from __future__ import annotations

from pathlib import Path

import pandas as pd


ASSETS_DIR = Path("assets/data/herold")


def ensure_assets_dir() -> Path:
    """Ensure the assets directory exists."""
    ASSETS_DIR.mkdir(parents=True, exist_ok=True)
    return ASSETS_DIR


def format_suffix_day_month_year(d) -> str:
    """Format date as DDMMYY to match existing artifacts (e.g., 100925)."""
    return d.strftime("%d%m%y")


def output_path(start_date, end_date) -> Path:
    folder = ensure_assets_dir()
    suffix = f"{format_suffix_day_month_year(start_date)}_{format_suffix_day_month_year(end_date)}"
    return folder / f"Herold_Data_{suffix}.xlsx"


def write_excel(
    *,
    df: pd.DataFrame,
    ser_ids: pd.DataFrame,
    start_date,
    end_date,
) -> Path:
    """Write main data and ids to Excel with two sheets.

    Sheets: "Unternehmensdaten" and "IDs" to mirror the notebook.
    File name: Herold_Data_<DDMMYY>_<DDMMYY>.xlsx under assets/data/herold.

    Args:
        df: pd.DataFrame
            Main data with "Unternehmensdaten" columns.
        ser_ids: pd.DataFrame
            IDs with "ids", "url", and "status" columns.
        start_date, end_date: date
            For naming the output file.
    """
    path = output_path(start_date, end_date)
    with pd.ExcelWriter(path) as writer:
        df.to_excel(writer, sheet_name="Unternehmensdaten", index=False)
        ser_ids.to_excel(writer, sheet_name="IDs", index=False)
    return path
