#!/usr/bin/env python3
"""Cache management utility for Wolf data collection."""

import argparse
from pathlib import Path
from src.utils.cache import Progress<PERSON><PERSON>


def list_cache(cache: ProgressCache):
    """List all cached operations."""
    operations = cache.list_cached_operations()
    
    if not operations:
        print("No cached operations found.")
        return
    
    print(f"Found {len(operations)} cached operations:\n")
    print(f"{'Client':<10} {'Start Date':<12} {'End Date':<12} {'Records':<8} {'Failed':<8} {'Complete':<8} {'Timestamp'}")
    print("-" * 80)
    
    for op in operations:
        completion = f"{op['completion_rate']*100:.1f}%"
        print(f"{op['client']:<10} {op['start_date']:<12} {op['end_date']:<12} "
              f"{op['total_records']:<8} {op['failed_count']:<8} {completion:<8} {op['timestamp'][:19]}")


def clear_cache(cache: ProgressCache, client: str = None, start_date: str = None, end_date: str = None):
    """Clear cache entries."""
    if client and start_date and end_date:
        cache.clear_cache(client, start_date, end_date)
        print(f"Cleared cache for {client} {start_date} to {end_date}")
    else:
        # Clear all cache files
        cache_dir = cache.cache_dir
        cache_files = list(cache_dir.glob("progress_*.json"))
        
        if not cache_files:
            print("No cache files to clear.")
            return
            
        for cache_file in cache_files:
            cache_file.unlink()
        
        print(f"Cleared {len(cache_files)} cache files.")


def show_cache_info(cache: ProgressCache):
    """Show cache directory information."""
    cache_dir = cache.cache_dir
    cache_files = list(cache_dir.glob("progress_*.json"))
    
    print(f"Cache directory: {cache_dir.absolute()}")
    print(f"Cache files: {len(cache_files)}")
    
    if cache_files:
        total_size = sum(f.stat().st_size for f in cache_files)
        print(f"Total cache size: {total_size / 1024:.1f} KB")


def main():
    parser = argparse.ArgumentParser(description="Manage Wolf data collection cache")
    parser.add_argument("--cache-dir", type=Path, help="Cache directory path")
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # List command
    subparsers.add_parser("list", help="List all cached operations")
    
    # Clear command
    clear_parser = subparsers.add_parser("clear", help="Clear cache entries")
    clear_parser.add_argument("--client", help="Client name (e.g., herold)")
    clear_parser.add_argument("--start-date", help="Start date (YYYY-MM-DD)")
    clear_parser.add_argument("--end-date", help="End date (YYYY-MM-DD)")
    clear_parser.add_argument("--all", action="store_true", help="Clear all cache files")
    
    # Info command
    subparsers.add_parser("info", help="Show cache directory information")
    
    args = parser.parse_args()
    
    # Create cache instance
    cache = ProgressCache(args.cache_dir)
    
    if args.command == "list":
        list_cache(cache)
    elif args.command == "clear":
        if args.all or (args.client and args.start_date and args.end_date):
            clear_cache(cache, args.client, args.start_date, args.end_date)
        else:
            print("For clear command, use --all or specify --client, --start-date, and --end-date")
    elif args.command == "info":
        show_cache_info(cache)
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
